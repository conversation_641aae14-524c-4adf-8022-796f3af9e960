# AI Reviewer Agent Rules for Code Reviews in V2

## Purpose
This document defines rules and guidelines for AI agents performing code reviews on merge requests in this project. Following these rules will ensure that reviews are thorough, consistent with project standards, and provide valuable feedback to developers.

## General Review Guidelines

1. **Understand Project Context First**
   - Before reviewing, analyze the project structure and patterns
   - Identify the architectural style (DDD, hexagonal, etc.) being used
   - Understand the project-specific conventions and standards

2. **Familiarize with Existing <PERSON><PERSON><PERSON>**
   - Review similar components in the codebase to understand established patterns
   - Note how similar features are implemented (repositories, services, controllers, etc.)
   - Identify naming conventions and code organization patterns

3. **Check for Consistency with Project Standards**
   - Ensure new code follows the same patterns as existing code
   - Verify that project-specific conventions are followed
   - Look for deviations from established patterns

## Specific Rules for PHP/Symfony/DDD Projects

1. **Domain-Driven Design Implementation**
   - Verify proper separation between domain and infrastructure
   - Check that domain objects are properly encapsulated and immutable when appropriate
   - Ensure domain logic is not leaking into infrastructure or application layers

2. **Object Handling and Immutability**
   - **CRITICAL**: Verify that collections and objects are properly cloned when returned from repositories
   - Check that objects are not passed by reference when they should be immutable
   - Ensure value objects are properly implemented as immutable

3. **Repository Pattern Implementation**
   - Verify that repositories follow the established pattern in the project
   - Check that Criteria pattern is used consistently for filtering
   - Ensure proper error handling with domain-specific exceptions
   - Verify that repository implementations don't expose infrastructure details

4. **Testing Configuration**
   - Ensure test services are properly configured
   - Verify that in-memory implementations are used in tests when appropriate
   - Check that test cases cover all repository methods and edge cases

5. **Code Optimization and Performance**
   - Look for unused code or imports
   - Check for potential performance issues in queries
   - Verify that appropriate indexes are used for database queries

6. **Project-Specific Conventions**
   - **CRITICAL**: Understand that the project prefers self-explanatory method and variable names over PHPDoc comments
   - Note that the project uses Criteria pattern extensively instead of convenience methods
   - Recognize that pagination is typically implemented through the Criteria base class
   - Understand that validation rules should only be implemented when business rules are defined
   - **CRITICAL**: Id and Uuid objects implement __toString() and can be used directly in array_unique() and setParameter() calls
   - **CRITICAL**: Prefer using Collection's reduce() method over array_reduce() for better type safety and consistency

## Review Process

1. **Before Starting the Review**
   - Check the issue that the MR is addressing to understand requirements
   - Look at similar components to understand expected patterns
   - Review project documentation for relevant standards

2. **During the Review**
   - Focus on both high-level architecture and low-level implementation details
   - Check for security, performance, and maintainability issues
   - Verify test coverage and test quality

3. **Providing Feedback**
   - Be specific about issues found, referencing exact files and lines
   - Explain why a change is recommended, not just what should be changed
   - Differentiate between critical issues and minor suggestions
   - Provide examples or code snippets when appropriate

4. **Follow-up**
   - Be prepared to explain your reasoning if questioned
   - Understand that project conventions may override general best practices
   - Be open to learning from project maintainers about project-specific approaches

## Common Pitfalls to Avoid

1. **Recommending General Best Practices Without Context**
   - Don't suggest adding PHPDoc when the project prefers self-explanatory names
   - Don't recommend convenience methods when the project standardizes on Criteria pattern
   - Don't suggest validation without understanding business rules

2. **Missing Project-Specific Patterns**
   - Don't overlook the need for proper cloning of objects and collections
   - Don't miss checking test service configurations
   - Don't ignore the project's approach to pagination through Criteria
   - Don't assume Id/Uuid objects need .value() extraction when they implement __toString()
   - Don't recommend array_reduce() over Collection's reduce() method

3. **Focusing Only on Surface-Level Issues**
   - Don't just check syntax and formatting
   - Don't ignore architectural concerns
   - Don't miss potential performance or security issues

By following these rules, AI reviewers will provide more valuable and contextually appropriate feedback, helping to maintain code quality while respecting project conventions.