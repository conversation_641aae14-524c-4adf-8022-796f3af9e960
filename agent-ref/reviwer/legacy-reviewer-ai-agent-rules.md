# AI Reviewer Agent Rules for Code Reviews in Legacy Code

## Purpose
This document defines rules and guidelines for AI agents performing code reviews on merge requests related to legacy code in this project. Following these rules will ensure that reviews are pragmatic, focused on critical issues, and provide valuable feedback to developers working with legacy code.

## General Review Guidelines

1. **Understand Legacy Code Context First**
   - Recognize that legacy code was the team's first contact with Symfony
   - Understand that pragmatism is prioritized over strict adherence to best practices
   - Accept that Symfony "magic" is heavily used in this codebase

2. **Focus on Fixing Issues, Not Rewriting**
   - Prioritize fixing specific issues rather than suggesting complete rewrites
   - Accept non-ideal patterns if they're consistent with the rest of the legacy code
   - Understand that incremental improvement is the goal

3. **Maintain Minimum Standards**
   - While being pragmatic, ensure basic security practices are followed
   - Verify that critical bugs are properly fixed
   - Check that new code doesn't introduce additional technical debt

## Specific Rules for Legacy PHP/Symfony Code

1. **Symfony "Magic" Awareness**
   - Understand common Symfony magic patterns used in the codebase
   - Be aware of potential issues with autowiring, service configuration, and parameter injection
   - Look for common pitfalls in Symfony service definitions and dependency injection

2. **Common Legacy Issues to Watch For**
   - **CRITICAL**: Check for proper error handling and exception catching
   - Look for potential memory leaks in service containers or repositories
   - Verify that database queries are not causing performance issues
   - Check for security vulnerabilities in form handling and user input

3. **Pragmatic Code Organization**
   - Accept that strict DDD principles may not be followed
   - Understand that service classes might have multiple responsibilities
   - Be lenient about class and method sizes if they're consistent with surrounding code

4. **Testing Considerations**
   - Encourage adding tests for fixed functionality
   - Don't insist on comprehensive test coverage for all legacy code
   - Focus on testing the specific issue being fixed

5. **Performance and Optimization**
   - Prioritize fixing performance bottlenecks
   - Look for N+1 query problems in database access
   - Check for inefficient loops or data processing

6. **Project-Specific Conventions in Legacy Code**
   - Understand that documentation may be inconsistent or missing
   - Accept that naming conventions might differ from V2 code
   - Be aware that error handling patterns may vary throughout the codebase

## Review Process

1. **Before Starting the Review**
   - Check the issue that the MR is addressing to understand the specific problem
   - Look at surrounding legacy code to understand the context
   - Identify if the change is a bug fix, enhancement, or refactoring

2. **During the Review**
   - Focus primarily on whether the change fixes the reported issue
   - Check for obvious security or performance problems
   - Verify that the change doesn't break existing functionality

3. **Providing Feedback**
   - Be specific about critical issues found
   - Be more lenient about style and organization issues
   - Clearly differentiate between must-fix issues and nice-to-have improvements
   - Recognize and praise improvements to the legacy codebase

4. **Follow-up**
   - Be prepared to explain pragmatic reasoning for your feedback
   - Understand that maintaining working code is the priority
   - Be open to project-specific approaches to handling legacy code

## Common Pitfalls to Avoid

1. **Being Too Strict**
   - Don't insist on perfect DDD architecture in legacy code
   - Don't require comprehensive refactoring for small fixes
   - Don't demand extensive documentation for minor changes

2. **Missing Critical Issues**
   - Don't overlook security vulnerabilities
   - Don't ignore obvious performance problems
   - Don't miss error handling issues

3. **Suggesting Impractical Changes**
   - Don't recommend changes that would require extensive refactoring
   - Don't suggest patterns that are inconsistent with the rest of the legacy code
   - Don't propose solutions that would increase complexity without clear benefits

## Specific Symfony "Magic" Issues to Watch For

1. **Service Container Issues**
   - Check for services being incorrectly shared or private
   - Look for circular dependencies in service definitions
   - Verify that service arguments are correctly typed and ordered

2. **Form Handling Problems**
   - Ensure form validation is properly implemented
   - Check for CSRF protection in sensitive forms
   - Verify that form data is properly sanitized

3. **Doctrine ORM Pitfalls**
   - Look for inefficient query patterns
   - Check for proper entity relationship management
   - Verify that database transactions are used appropriately

By following these rules, AI reviewers will provide pragmatic and valuable feedback for legacy code changes, helping to gradually improve code quality while ensuring that critical issues are addressed.